<template>
  <div class="user-info-container">
    <div
      class="membership-card normal-member"
      :class="{ clickable: normalMember.remaining === 0 }"
      @click="purchaseMembership"
    >
      <div class="membership-header">
        <span style="font-weight: 500">{{ normalMember.title }}</span>
        <img :src="normalMember.icon" alt="" />
      </div>
      <div class="membership-body">
        <span>剩余可提交</span>
        <span class="submission-count">
          <span class="remaining">{{ normalMember.remaining }}</span>
          <span>/{{ normalMember.total }}</span>
        </span>
        <span v-if="normalMember.remaining === 0" class="purchase-link">购买会员</span>
      </div>
    </div>

    <UserProfile />
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import IconVip from '@/assets/icons/vip.svg'
import UserProfile from './UserProfile.vue'

const userStore = useUserStore()

// 从用户信息计算会员数据
const normalMember = computed(() => {
  const userInfo = userStore.userInfo
  return {
    title: '普通会员',
    icon: IconVip,
    remaining: userInfo.remainingTimes,
    total: userInfo.totalTimes,
  }
})

const purchaseMembership = () => {
  if (normalMember.value.remaining === 0) {
    console.log('Purchase membership clicked')
    // a-modal
  }
}

// 组件挂载时获取用户信息
onMounted(async () => {
  if (userStore.isLoggedIn && !userStore.userInfo.id) {
    try {
      await userStore.fetchUserProfile()
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }
})
</script>

<style scoped>
.user-info-container {
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 4px;
  flex-shrink: 0; /* Prevent shrinking */
}

.membership-card {
  border-radius: 8px;
  background-color: #ffffff;
  padding: 16px;
  font-size: 14px;
  font-family: 'HarmonyOS Sans', sans-serif;
  color: #7f8792;
  position: relative;
  overflow: hidden;
}

.membership-header {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.membership-header img {
  width: 27px;
  height: 12px;
  margin-left: 4px;
}

.membership-body {
  margin-top: 12px;
  display: flex;
  align-items: center;
  font-weight: 400;
}

.submission-count .remaining {
  margin-left: 8px;
  color: #0057d9;
}

.purchase-link {
  color: #0057d9;
  margin-left: 14px;
  text-decoration: none;
}

.membership-card.clickable:hover {
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.alliance-member {
  color: #0057d9;
}

.alliance-title {
  color: #0057d9;
}

.diamond-icon {
  position: absolute;
  top: 10px;
  right: 4px;
  width: 81px;
  height: 58px;
  object-fit: cover;
  opacity: 1;
}
</style>
