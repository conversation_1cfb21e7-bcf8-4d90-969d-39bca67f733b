import request from '@/utils/request'

// ==================== 对话相关接口 ====================

/**
 * 创建法律文书检查对话
 * @param {number} type - 对话类型：1-判决书，2-处罚决定书，3-租赁权拍卖公告，4-合同
 * @returns {Promise}
 */
export function createConversation(type) {
  return request.post('/app-api/law/check/conversation/create', {
    type,
  })
}

/**
 * 更新法律文书检查对话
 * @param {object} data - 更新数据
 * @returns {Promise}
 */
export function updateConversation(data) {
  return request.put('/app-api/law/check/conversation/update', data)
}

/**
 * 获得【我的】对话列表
 * @param {string} type - 对话类型（可选）
 * @param {number} pageNo - 页码（默认1）
 * @param {number} pageSize - 每页大小（默认20）
 * @returns {Promise}
 */
export function getMyConversationList(type, pageNo = 1, pageSize = 20) {
  const params = { pageNo, pageSize }
  if (type) {
    params.type = type
  }
  return request.get('/app-api/law/check/conversation/my-list', params)
}

/**
 * 获得法律文书检查对话分页
 * @param {object} params - 查询参数
 * @returns {Promise}
 */
export function getConversationPage(params) {
  return request.get('/app-api/law/check/conversation/page', params)
}

/**
 * 获得法律文书检查对话详情
 * @param {number} id - 对话ID
 * @returns {Promise}
 */
export function getConversation(id) {
  return request.get('/app-api/law/check/conversation/get', { id })
}

/**
 * 删除法律文书检查对话
 * @param {number} id - 对话ID
 * @returns {Promise}
 */
export function deleteConversation(id) {
  return request.delete('/app-api/law/check/conversation/delete', { id })
}

// ==================== 任务相关接口 ====================

/**
 * 创建法律文书检查任务
 * @param {object} data - 任务数据
 * @returns {Promise}
 */
export function createTask(data) {
  return request.post('/app-api/law/check/task/create', data)
}

/**
 * 更新法律文书检查任务
 * @param {object} data - 任务数据
 * @returns {Promise}
 */
export function updateTask(data) {
  return request.put('/app-api/law/check/task/update', data)
}

/**
 * 提交任务
 * @param {number} id - 任务ID
 * @returns {Promise}
 */
export function submitTask(id) {
  return request.post('/app-api/law/check/task/submit', { id })
}

/**
 * 获得法律文书检查任务分页
 * @param {object} params - 查询参数
 * @returns {Promise}
 */
export function getTaskPage(params) {
  return request.get('/app-api/law/check/task/page', params)
}

/**
 * 获得任务列表
 * @param {object} params - 查询参数
 * @param {number} pageNo - 页码（默认1）
 * @param {number} pageSize - 每页大小（默认20）
 * @returns {Promise}
 */
export function getTaskList(params, pageNo = 1, pageSize = 20) {
  const requestParams = {
    ...params,
    pageNo,
    pageSize,
  }
  return request.get('/app-api/law/check/task/list', requestParams)
}

/**
 * 获得法律文书检查任务详情
 * @param {number} id - 任务ID
 * @returns {Promise}
 */
export function getTask(id) {
  return request.get('/app-api/law/check/task/get', { id })
}

/**
 * 删除法律文书检查任务
 * @param {number} id - 任务ID
 * @returns {Promise}
 */
export function deleteTask(id) {
  return request.delete('/app-api/law/check/task/delete', { id })
}

/**
 * 批量删除法律文书检查任务
 * @param {number[]} ids - 任务ID数组
 * @returns {Promise}
 */
export function deleteTaskList(ids) {
  return request.delete('/app-api/law/check/task/delete-list', { ids })
}

// ==================== 文件上传接口 ====================

/**
 * 上传文件
 * @param {File} file - 文件对象
 * @param {string} directory - 文件目录（可选）
 * @returns {Promise}
 */
export function uploadFile(file, directory = 'law-app') {
  const formData = new FormData()
  formData.append('file', file)

  return request.post('/app-api/infra/file/upload-detail', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    params: {
      directory,
    },
  })
}
