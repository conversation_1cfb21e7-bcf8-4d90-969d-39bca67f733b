<script setup>
import { computed } from 'vue'
import loadingIcon from '@/assets/icons/loading.svg'
import completedIcon from '@/assets/icons/upload-done.svg'

const props = defineProps({
  fileName: {
    type: String,
    default: '20250701XX法院诉讼文件.pdf',
  },
  fileSize: {
    type: String,
    default: '3.2 MB',
  },
  progress: {
    type: Number,
    default: 50,
  },
  status: {
    type: String,
    default: 'uploading', // 'uploading', 'creating', 'submitting', 'completed', 'error'
  },
  error: {
    type: String,
    default: '',
  },
  taskId: {
    type: [Number, String],
    default: null,
  },
  resultFileUrl: {
    type: String,
    default: '',
  },
})

const isUploading = computed(() => props.status === 'uploading')
const isCreating = computed(() => props.status === 'creating')
const isSubmitting = computed(() => props.status === 'submitting')
const isCompleted = computed(() => props.status === 'completed')
const isError = computed(() => props.status === 'error')
const isPending = computed(() => props.status === 'pending')
const isReviewing = computed(() => props.status === 'reviewing')
const isAnalyzing = computed(() => props.status === 'analyzing')
const isRejected = computed(() => props.status === 'rejected')

const statusText = computed(() => {
  if (isUploading.value) return '正在上传文件...'
  if (isCreating.value) return '正在创建任务...'
  if (isSubmitting.value) return '正在提交任务...'
  if (isPending.value) return '待提交'
  if (isReviewing.value) return '待审核'
  if (isAnalyzing.value) return '分析中...'
  if (isCompleted.value) return '已完成'
  if (isRejected.value) return '已驳回'
  if (isError.value) return props.error || '上传失败'
  return ''
})

const statusIcon = computed(() => {
  if (isUploading.value || isCreating.value || isSubmitting.value || isAnalyzing.value)
    return loadingIcon
  if (isCompleted.value) return completedIcon
  return ''
})

const showDownloadButton = computed(() => {
  return isCompleted.value && props.resultFileUrl
})

const downloadResult = () => {
  if (props.resultFileUrl) {
    window.open(props.resultFileUrl, '_blank')
  }
}

const showRetryButton = computed(() => {
  return isError.value
})

const showSubmitButton = computed(() => {
  return isPending.value && props.taskId
})

const hasActions = computed(() => {
  return showRetryButton.value || showSubmitButton.value || showDownloadButton.value
})

const retryUpload = () => {
  // 这里可以触发重新上传的逻辑
  console.log('重新上传文件')
}

const emit = defineEmits(['submit-task'])

const submitTask = () => {
  if (props.taskId) {
    emit('submit-task', props.taskId)
  }
}
</script>

<template>
  <div class="upload-item" :class="{ 'has-actions': hasActions }">
    <!-- 文件图标 -->
    <div class="file-icon">
      <img src="@/assets/icons/pdf.svg" alt="PDF Icon" />
    </div>

    <!-- 文件信息 -->
    <div class="file-info">
      <div class="file-details">
        <div class="file-name">{{ fileName }}</div>
        <div class="file-size">{{ fileSize }}</div>
      </div>
    </div>

    <!-- 状态信息 -->
    <div class="status-section">
      <div class="upload-status">
        <img
          v-if="statusIcon"
          :src="statusIcon"
          :class="{
            'icon-loading': isUploading || isCreating || isSubmitting || isAnalyzing,
          }"
          alt="Status Icon"
        />
        <span :class="{ 'error-text': isError }">{{ statusText }}</span>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div v-if="hasActions" class="action-section">
      <button v-if="showRetryButton" class="btn btn-primary" @click="retryUpload">重试</button>
      <button v-if="showSubmitButton" class="btn btn-primary" @click="submitTask">提交</button>
      <button v-if="showDownloadButton" class="btn btn-success" @click="downloadResult">
        下载报告
      </button>
    </div>
  </div>
</template>

<style scoped>
.upload-item {
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  gap: 16px;
  border-radius: 8px;
  background-color: #f8f9fa;
  padding: 16px;
}

.upload-item.has-actions {
  grid-template-columns: auto 1fr auto auto;
}

/* 文件图标 */
.file-icon {
  grid-column: 1;
}

/* 文件信息 */
.file-info {
  grid-column: 2;
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 0;
}

.file-icon {
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border: 1px solid #ebebeb;
  border-radius: 8px;
}

.file-icon img {
  width: 28px;
  height: 28px;
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #222529;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 12px;
  color: #7f8792;
  line-height: 1.2;
}

/* 状态信息 */
.status-section {
  grid-column: 3;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.upload-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #222529;
}

.upload-status img {
  width: 16px;
  height: 16px;
}

/* 操作按钮 */
.action-section {
  grid-column: 4;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
}

.error-text {
  color: #dc3545;
}

.btn {
  padding: 6px 16px;
  border-radius: 20px; /* 圆角按钮 */
  border: 1px solid;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  background-color: transparent; /* 幽灵按钮 */
}

.btn-primary {
  border-color: #0057d9;
  background-color: transparent; /* 幽灵按钮 */
  color: #0057d9;
}

.btn-primary:hover {
  background-color: #0057d9;
  color: #fff;
}

.btn-success {
  border-color: #28a745;
  background-color: transparent; /* 幽灵按钮 */
  color: #28a745;
}

.btn-success:hover {
  background-color: #28a745;
  color: #fff;
}

.icon-loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
