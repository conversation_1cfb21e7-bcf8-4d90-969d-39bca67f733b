import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API, // api 的 base_url
  timeout: 60 * 3 * 1000,
})

// request 拦截器
service.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    // 例如，添加 token
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers['Authorization'] = 'Bearer ' + userStore.token
    }
    return config
  },
  (error) => {
    // 对请求错误做些什么
    console.log(error) // for debug
    ElMessage.error('请求发送失败')
    return Promise.reject(error)
  },
)

// response 拦截器
service.interceptors.response.use(
  (response) => {
    // 对响应数据做些什么
    const res = response.data
    // 如果状态码不是 200，则认为有错误
    if (res.code !== 0) {
      // 显示错误消息
      ElMessage.error(res.msg || 'Error')

      // 401: Token 过期或无效
      if (res.code === 401) {
        const userStore = useUserStore()
        ElMessage.error('登录已过期，请重新登录')
        userStore.resetState()
        // 跳转到登录页，避免循环导入，使用 window.location
        if (window.location.pathname !== '/login') {
          window.location.href = '/login'
        }
      }
      return Promise.reject(new Error(res.msg || 'Error'))
    } else {
      return res
    }
  },
  (error) => {
    // 对响应错误做些什么
    console.log('err' + error) // for debug

    // 处理网络错误
    if (error.response) {
      const { status, data } = error.response

      if (status === 401) {
        const userStore = useUserStore()
        ElMessage.error('登录已过期，请重新登录')
        userStore.resetState()
        if (window.location.pathname !== '/login') {
          window.location.href = '/login'
        }
      } else if (status === 403) {
        ElMessage.error('没有权限访问该资源')
      } else if (status === 404) {
        ElMessage.error('请求的资源不存在')
      } else if (status >= 500) {
        ElMessage.error('服务器内部错误')
      } else {
        ElMessage.error(data?.msg || error.message || '请求失败')
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络')
    } else {
      ElMessage.error(error.message || '请求失败')
    }

    return Promise.reject(error)
  },
)

// 封装请求方法
const request = {
  get(url, params) {
    return service({
      url: url,
      method: 'get',
      params,
    })
  },
  post(url, data) {
    return service({
      url: url,
      method: 'post',
      data,
    })
  },
  put(url, data) {
    return service({
      url: url,
      method: 'put',
      data,
    })
  },
  delete(url, data) {
    return service({
      url: url,
      method: 'delete',
      data,
    })
  },
}

export default request
